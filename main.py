#!/usr/bin/env python3
"""
ML Log Prediction with GUI file selection dialog for LAS files.
"""

from data_handler import load_las_files_from_directory, clean_log_data, write_results_to_las
from config_handler import get_input_files, select_output_directory, configure_log_selection, configure_well_separation, get_prediction_mode, configure_hyperparameters
from ml_core import impute_logs, MODEL_REGISTRY
from reporting import generate_qc_report, create_summary_plots, generate_final_report

# Import SAITS GPU optimization
try:
    from saits_gpu_optimizer import SAITSGPUOptimizer, patch_existing_saits_for_gpu, get_gpu_optimization_recommendations
    import torch
    SAITS_GPU_AVAILABLE = True
except ImportError:
    SAITS_GPU_AVAILABLE = False

def check_and_configure_gpu_optimization():
    """Check GPU availability and configure SAITS optimization if available."""
    if not SAITS_GPU_AVAILABLE:
        print("⚠️  SAITS GPU optimization not available")
        return False

    print("\n🚀 GPU Optimization Check")
    print("=" * 40)

    # Check if CUDA is available
    if not torch.cuda.is_available():
        print("❌ CUDA not available - using CPU only")
        print("💡 To enable GPU acceleration:")
        print("   1. Install NVIDIA drivers")
        print("   2. Install CUDA toolkit")
        print("   3. Install PyTorch with CUDA support")
        return False

    # Show GPU information
    gpu_count = torch.cuda.device_count()
    print(f"✅ CUDA available with {gpu_count} GPU(s)")

    for i in range(gpu_count):
        props = torch.cuda.get_device_properties(i)
        memory_gb = props.total_memory / 1024**3
        print(f"   GPU {i}: {props.name} ({memory_gb:.1f} GB)")

    # Ask user if they want to enable GPU optimization
    print("\nDo you want to enable GPU optimization for SAITS models?")
    print("1. Yes, enable GPU optimization (recommended)")
    print("2. No, use CPU only")

    while True:
        choice = input("Enter choice (1 or 2): ").strip()
        if choice == "1":
            print("🚀 Enabling GPU optimization for SAITS models...")

            # Apply GPU optimization patches
            patch_existing_saits_for_gpu()

            # Show optimization recommendations
            print("\n📋 GPU Optimization Recommendations:")
            get_gpu_optimization_recommendations()

            return True
        elif choice == "2":
            print("📊 Using CPU-only mode")
            return False
        else:
            print("Invalid choice. Please enter 1 or 2.")

def optimize_saits_hyperparameters(hparams, gpu_enabled):
    """Optimize SAITS hyperparameters based on GPU availability."""
    if not gpu_enabled or not SAITS_GPU_AVAILABLE:
        return hparams

    print("\n🎯 Optimizing SAITS hyperparameters for GPU...")

    # Create GPU optimizer
    optimizer = SAITSGPUOptimizer()

    # Optimize SAITS hyperparameters if available
    if 'saits' in hparams:
        original_params = hparams['saits'].copy()
        optimized_params = optimizer.optimize_hyperparameters_for_gpu(original_params)
        hparams['saits'] = optimized_params

        print("   SAITS hyperparameters optimized for GPU:")
        for key, value in optimized_params.items():
            if key in original_params and original_params[key] != value:
                print(f"     {key}: {original_params[key]} → {value}")

    # Optimize Simple SAITS hyperparameters if available
    if 'simple_saits' in hparams:
        original_params = hparams['simple_saits'].copy()
        optimized_params = optimizer.optimize_hyperparameters_for_gpu(original_params)
        hparams['simple_saits'] = optimized_params

        print("   Simple SAITS hyperparameters optimized for GPU:")
        for key, value in optimized_params.items():
            if key in original_params and original_params[key] != value:
                print(f"     {key}: {original_params[key]} → {value}")

    return hparams

def main():
    """Run the ML log prediction workflow with GUI file selection."""
    print("=" * 60)
    print(" ML LOG PREDICTION")
    print("=" * 60)
    
    # Step 1: Get input files using file dialog
    print("\n📁 Step 1: Select LAS files")
    inp = get_input_files()
    
    if not inp:
        print("❌ File selection cancelled. Exiting.")
        return
    
    # Step 2: Load LAS files
    print("\n📊 Step 2: Loading LAS files...")
    df, las_objs, wells, logs = load_las_files_from_directory(inp)
    
    if df.empty:
        print("❌ No data loaded. Exiting.")
        return
    
    print(f"✅ Successfully loaded:")
    print(f"   • {len(df)} data points")
    print(f"   • {len(wells)} wells: {', '.join(wells)}")
    print(f"   • {len(logs)} log curves: {', '.join(logs)}")
    
    # Step 3: Configure log selection
    print("\n🎯 Step 3: Configure feature and target logs")
    feats, tgt = configure_log_selection(logs)
    print(f"✅ Feature logs: {', '.join(feats)}")
    print(f"✅ Target log: {tgt}")
    
    # Step 4: Configure well separation
    print("\n🏗️ Step 4: Configure training/prediction strategy")
    cfg = configure_well_separation(wells)
    print(f"✅ Mode: {cfg['mode']}")
    
    # Step 5: Configure prediction mode
    print("\n⚙️ Step 5: Configure prediction mode")
    mode = get_prediction_mode()
    print(f"✅ Prediction mode: {mode}")
    
    # Step 6: Configure hyperparameters
    print("\n🔧 Step 6: Configure model hyperparameters")
    hparams = configure_hyperparameters()
    print("✅ Using default hyperparameters for all models")

    # Step 6.5: Check and configure GPU optimization for SAITS
    gpu_enabled = check_and_configure_gpu_optimization()

    # Step 6.6: Optimize SAITS hyperparameters for GPU if enabled
    if gpu_enabled:
        hparams = optimize_saits_hyperparameters(hparams, gpu_enabled)

    # Step 7: Data cleaning and QC
    print("\n🧹 Step 7: Data cleaning and quality control")
    clean_df = clean_log_data(df)
    generate_qc_report(clean_df, feats+[tgt], cfg)
    
    # Step 8: Machine learning prediction
    print("\n🤖 Step 8: Running machine learning models...")
    models = {MODEL_REGISTRY[k]['name']: MODEL_REGISTRY[k]['model_class'](**hparams[k]) for k in MODEL_REGISTRY}
    res_df, mres = impute_logs(clean_df, feats, tgt, models, cfg, mode)
    
    if not mres:
        print("❌ Model training failed. Exiting.")
        return
    
    print("✅ Machine learning prediction completed")
    
    # Step 9: Configure output options
    print("\n💾 Step 9: Configure output options")
    print("Do you want to save the results?")
    print("1. Yes, save results to files")
    print("2. No, just plot and test (no file output)")
    
    while True:
        choice = input("Enter choice (1 or 2): ").strip()
        if choice == "1":
            # Get output directory
            print("\n📁 Select output directory...")
            out = select_output_directory()
            if not out:
                print("❌ Output directory selection cancelled. Exiting.")
                return
            
            # Generate and save results
            print("\n📈 Step 10: Generating and saving results...")
            create_summary_plots(res_df, mres, cfg)
            write_results_to_las(res_df, tgt, las_objs, out)
            generate_final_report(mres, hparams)
            
            print("\n🎉 Workflow completed successfully!")
            print(f"📁 Results saved to: {out}")

            # Show GPU optimization status
            if gpu_enabled:
                print("🚀 GPU optimization was enabled for SAITS models")
            elif SAITS_GPU_AVAILABLE and torch.cuda.is_available():
                print("💡 GPU optimization was available but not used")

            break
        elif choice == "2":
            # Just show plots and testing without saving
            print("\n📈 Step 10: Generating plots and testing (no file output)...")
            create_summary_plots(res_df, mres, cfg)
            
            print("\n🎉 Analysis completed successfully!")
            print("📊 Plots generated for review (no files saved)")

            # Show GPU optimization status
            if gpu_enabled:
                print("🚀 GPU optimization was enabled for SAITS models")
            elif SAITS_GPU_AVAILABLE and torch.cuda.is_available():
                print("💡 GPU optimization was available but not used")

            break
        else:
            print("Invalid choice. Please enter 1 or 2.")

if __name__ == "__main__":
    main()
