#!/usr/bin/env python3
"""
Speed test for SAITS with different optimization levels.
"""

import time
import numpy as np
import pandas as pd
import torch
from sklearn.model_selection import train_test_split
from sklearn.datasets import make_regression

# Import your existing SAITS
try:
    from saits_model import SAITSRegressor
    SAITS_AVAILABLE = True
except ImportError:
    print("⚠️  SAITSRegressor not available")
    SAITS_AVAILABLE = False

try:
    from simple_saits import SimpleSAITSRegressor
    SIMPLE_SAITS_AVAILABLE = True
except ImportError:
    print("⚠️  SimpleSAITSRegressor not available")
    SIMPLE_SAITS_AVAILABLE = False

from saits_gpu_optimizer import SAITSGPUOptimizer, patch_existing_saits_for_gpu


def create_test_data(n_samples=1000, n_features=4, missing_rate=0.2):
    """Create test data with missing values."""
    print(f"📊 Creating test data: {n_samples} samples, {n_features} features, {missing_rate:.1%} missing")
    
    # Generate synthetic data
    X, y = make_regression(n_samples=n_samples, n_features=n_features, noise=0.1, random_state=42)
    
    # Add missing values
    mask = np.random.random(X.shape) < missing_rate
    X[mask] = np.nan
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    print(f"   Training set: {X_train.shape[0]} samples")
    print(f"   Test set: {X_test.shape[0]} samples")
    print(f"   Missing values in training: {np.isnan(X_train).sum() / X_train.size:.1%}")
    
    return X_train, X_test, y_train, y_test


def benchmark_saits_configurations():
    """Benchmark different SAITS configurations."""
    print("=" * 80)
    print(" SAITS SPEED BENCHMARK")
    print("=" * 80)
    
    # Create test data
    X_train, X_test, y_train, y_test = create_test_data(n_samples=2000, n_features=4)
    
    # Test configurations
    configs = [
        {
            'name': 'Baseline (CPU, small)',
            'params': {
                'sequence_length': 30,
                'batch_size': 16,
                'd_model': 32,
                'd_inner': 64,
                'epochs': 10,
                'patience': 5
            },
            'force_cpu': True,
            'use_optimization': False
        },
        {
            'name': 'GPU Standard',
            'params': {
                'sequence_length': 50,
                'batch_size': 32,
                'd_model': 64,
                'd_inner': 128,
                'epochs': 10,
                'patience': 5
            },
            'force_cpu': False,
            'use_optimization': False
        },
        {
            'name': 'GPU Optimized',
            'params': {
                'sequence_length': 75,
                'batch_size': 64,
                'd_model': 128,
                'd_inner': 256,
                'epochs': 10,
                'patience': 5
            },
            'force_cpu': False,
            'use_optimization': True
        }
    ]
    
    results = []
    
    for config in configs:
        print(f"\n🧪 Testing: {config['name']}")
        print(f"   Parameters: {config['params']}")
        
        # Skip GPU tests if CUDA not available
        if not config['force_cpu'] and not torch.cuda.is_available():
            print("   ⏭️  Skipping (CUDA not available)")
            continue
        
        try:
            # Create model
            if SAITS_AVAILABLE:
                model = SAITSRegressor(**config['params'])
            elif SIMPLE_SAITS_AVAILABLE:
                model = SimpleSAITSRegressor(**config['params'])
            else:
                print("   ❌ No SAITS implementation available")
                continue
            
            # Force CPU if requested
            if config['force_cpu']:
                model.device = torch.device('cpu')

            # Apply optimizations
            if config['use_optimization']:
                optimizer = SAITSGPUOptimizer()
                # This would be applied during training

            # Measure training time
            print("   🏋️  Training...")
            start_time = time.time()

            # Fit with or without GPU optimization based on config
            use_gpu_opt = not config['force_cpu'] and config.get('use_optimization', False)
            model.fit(X_train, y_train, verbose=False, use_gpu_optimization=use_gpu_opt)
            
            training_time = time.time() - start_time
            
            # Measure prediction time
            print("   🔮 Predicting...")
            start_time = time.time()
            
            predictions = model.predict(X_test)

            prediction_time = time.time() - start_time

            # Handle NaN predictions (expected for sequence models)
            valid_mask = ~np.isnan(predictions)
            if valid_mask.sum() == 0:
                print(f"   ⚠️  All predictions are NaN - model may need more training")
                continue

            valid_predictions = predictions[valid_mask]
            valid_y_test = y_test[valid_mask]

            print(f"   📊 Valid predictions: {valid_mask.sum()}/{len(predictions)}")

            # Calculate metrics
            from sklearn.metrics import mean_absolute_error, r2_score
            mae = mean_absolute_error(valid_y_test, valid_predictions)
            r2 = r2_score(valid_y_test, valid_predictions)
            
            result = {
                'config': config['name'],
                'training_time': training_time,
                'prediction_time': prediction_time,
                'total_time': training_time + prediction_time,
                'mae': mae,
                'r2': r2,
                'device': str(model.device) if hasattr(model, 'device') else 'unknown'
            }
            
            results.append(result)
            
            print(f"   ✅ Completed in {training_time:.1f}s training + {prediction_time:.1f}s prediction")
            print(f"   📊 MAE: {mae:.4f}, R²: {r2:.4f}")
            
        except Exception as e:
            print(f"   ❌ Failed: {e}")
            continue
    
    # Print summary
    print("\n" + "=" * 80)
    print(" BENCHMARK RESULTS")
    print("=" * 80)
    
    if results:
        # Create results table
        print(f"{'Configuration':<20} {'Device':<10} {'Train(s)':<10} {'Pred(s)':<10} {'Total(s)':<10} {'MAE':<10} {'R²':<10}")
        print("-" * 80)
        
        baseline_time = None
        for result in results:
            if 'Baseline' in result['config']:
                baseline_time = result['total_time']
            
            speedup = ""
            if baseline_time and result['total_time'] < baseline_time:
                speedup = f" ({baseline_time/result['total_time']:.1f}x faster)"
            
            print(f"{result['config']:<20} {result['device']:<10} {result['training_time']:<10.1f} "
                  f"{result['prediction_time']:<10.1f} {result['total_time']:<10.1f} "
                  f"{result['mae']:<10.4f} {result['r2']:<10.3f}{speedup}")
        
        # Find best configuration
        best_config = min(results, key=lambda x: x['total_time'])
        print(f"\n🏆 Fastest configuration: {best_config['config']} ({best_config['total_time']:.1f}s total)")
        
        if torch.cuda.is_available():
            gpu_results = [r for r in results if 'cuda' in r['device'].lower()]
            if gpu_results:
                best_gpu = min(gpu_results, key=lambda x: x['total_time'])
                print(f"🚀 Best GPU configuration: {best_gpu['config']} ({best_gpu['total_time']:.1f}s total)")
    else:
        print("❌ No successful benchmark runs")
    
    return results


def quick_gpu_check():
    """Quick check of GPU acceleration potential."""
    print("=" * 60)
    print(" QUICK GPU ACCELERATION CHECK")
    print("=" * 60)
    
    if not torch.cuda.is_available():
        print("❌ CUDA not available")
        print("\nTo enable GPU acceleration:")
        print("1. Install NVIDIA drivers")
        print("2. Install CUDA toolkit")
        print("3. Install PyTorch with CUDA:")
        print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
        return False
    
    # GPU info
    gpu_count = torch.cuda.device_count()
    print(f"✅ CUDA available with {gpu_count} GPU(s)")
    
    for i in range(gpu_count):
        props = torch.cuda.get_device_properties(i)
        memory_gb = props.total_memory / 1024**3
        print(f"   GPU {i}: {props.name} ({memory_gb:.1f} GB)")
    
    # Quick speed test
    print("\n🏃 Quick speed test...")
    device_cpu = torch.device('cpu')
    device_gpu = torch.device('cuda:0')
    
    # Test data
    x = torch.randn(1000, 100, 64)
    
    # CPU test
    x_cpu = x.to(device_cpu)
    start = time.time()
    for _ in range(100):
        _ = torch.matmul(x_cpu, x_cpu.transpose(-2, -1))
    cpu_time = time.time() - start
    
    # GPU test
    x_gpu = x.to(device_gpu)
    torch.cuda.synchronize()
    start = time.time()
    for _ in range(100):
        _ = torch.matmul(x_gpu, x_gpu.transpose(-2, -1))
    torch.cuda.synchronize()
    gpu_time = time.time() - start
    
    speedup = cpu_time / gpu_time
    print(f"   CPU time: {cpu_time:.3f}s")
    print(f"   GPU time: {gpu_time:.3f}s")
    print(f"   🚀 GPU is {speedup:.1f}x faster")
    
    if speedup > 2:
        print("✅ Good GPU acceleration potential!")
    elif speedup > 1.2:
        print("⚠️  Moderate GPU acceleration")
    else:
        print("❌ Poor GPU acceleration (check GPU utilization)")
    
    return True


if __name__ == "__main__":
    # Quick GPU check
    gpu_available = quick_gpu_check()
    
    if gpu_available or torch.cuda.is_available():
        # Patch SAITS for optimizations
        patch_existing_saits_for_gpu()
        
        # Run benchmark
        results = benchmark_saits_configurations()
        
        print("\n" + "=" * 60)
        print(" OPTIMIZATION RECOMMENDATIONS")
        print("=" * 60)
        
        if torch.cuda.is_available():
            print("🎯 For maximum SAITS speed:")
            print("1. Use larger batch sizes (64-128)")
            print("2. Increase model dimensions (d_model=128+)")
            print("3. Enable mixed precision training")
            print("4. Use torch.compile() if available")
            print("5. Increase sequence_length for better GPU utilization")
        else:
            print("🎯 For CPU optimization:")
            print("1. Use smaller batch sizes (16-32)")
            print("2. Reduce model dimensions if needed")
            print("3. Enable multi-threading")
            print("4. Consider using SimpleSAITS for faster training")
    else:
        print("\n💡 Install CUDA-enabled PyTorch for significant speedup!")
