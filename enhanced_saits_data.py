#!/usr/bin/env python3
"""
Enhanced SAITS Data Handler inspired by PyPOTS implementation.

This module provides sophisticated data handling capabilities for SAITS training,
including proper mask generation, artificial missing value creation, and
advanced data preprocessing techniques.
"""

import torch
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
import warnings


class EnhancedSAITSDataset(Dataset):
    """
    Enhanced dataset class for SAITS training with PyPOTS-inspired features.
    
    This dataset handles:
    - Artificial missing value generation for MIT training
    - Proper mask creation for ORT and MIT losses
    - Sequence generation for time series data
    - Advanced missing value patterns
    
    Args:
        X (np.ndarray): Input time series data
        y (np.ndarray, optional): Target values for supervised learning
        sequence_length (int): Length of sequences to generate
        missing_rate (float): Rate of artificial missing values for training
        return_X_ori (bool): Whether to return original data for MIT loss
        missing_pattern (str): Pattern for artificial missing values
        standardize (bool): Whether to standardize the data
    """
    
    def __init__(self, X, y=None, sequence_length=50, missing_rate=0.1, 
                 return_X_ori=True, missing_pattern='MCAR', standardize=True):
        
        self.sequence_length = sequence_length
        self.missing_rate = missing_rate
        self.return_X_ori = return_X_ori
        self.missing_pattern = missing_pattern
        self.return_y = y is not None
        
        # Store original data
        self.X_original = X.copy()
        self.y_original = y.copy() if y is not None else None
        
        # Standardize data if requested
        if standardize:
            self.scaler_X = StandardScaler()
            self.X_scaled = self.scaler_X.fit_transform(
                X.reshape(-1, X.shape[-1])
            ).reshape(X.shape)
            
            if self.return_y:
                self.scaler_y = StandardScaler()
                self.y_scaled = self.scaler_y.fit_transform(y.reshape(-1, 1)).flatten()
            else:
                self.scaler_y = None
                self.y_scaled = None
        else:
            self.scaler_X = None
            self.scaler_y = None
            self.X_scaled = X.copy()
            self.y_scaled = y.copy() if y is not None else None
        
        # Generate sequences
        self.sequences = self._generate_sequences()
        
    def _generate_sequences(self):
        """Generate sequences from the time series data."""
        sequences = []
        X_data = self.X_scaled
        y_data = self.y_scaled
        
        # Generate sequences
        for i in range(len(X_data) - self.sequence_length + 1):
            seq_X = X_data[i:i + self.sequence_length]
            
            # Check if sequence has enough valid data
            if not np.all(np.isnan(seq_X)):
                seq_data = {'X': seq_X, 'index': i}
                
                if self.return_y:
                    # Use the target value at the end of the sequence
                    seq_data['y'] = y_data[i + self.sequence_length - 1]
                
                sequences.append(seq_data)
        
        return sequences
    
    def _create_artificial_missing(self, X):
        """
        Create artificial missing values based on the specified pattern.
        
        Args:
            X (np.ndarray): Input sequence
            
        Returns:
            tuple: (X_with_missing, artificial_mask)
        """
        if self.missing_pattern == 'MCAR':
            # Missing Completely At Random
            artificial_mask = np.random.random(X.shape) < self.missing_rate
        elif self.missing_pattern == 'MAR':
            # Missing At Random (simplified implementation)
            # Make missingness depend on observed values
            threshold = np.nanpercentile(X[~np.isnan(X)], 50)  # Median
            prob_missing = np.where(X > threshold, self.missing_rate * 1.5, self.missing_rate * 0.5)
            artificial_mask = np.random.random(X.shape) < prob_missing
        elif self.missing_pattern == 'MNAR':
            # Missing Not At Random (simplified implementation)
            # Higher values are more likely to be missing
            prob_missing = np.clip(
                (X - np.nanmin(X)) / (np.nanmax(X) - np.nanmin(X)) * self.missing_rate * 2,
                0, self.missing_rate * 2
            )
            artificial_mask = np.random.random(X.shape) < prob_missing
        else:
            raise ValueError(f"Unsupported missing pattern: {self.missing_pattern}")
        
        # Don't make already missing values "artificially missing"
        artificial_mask = artificial_mask & ~np.isnan(X)
        
        # Create data with artificial missing values
        X_with_missing = X.copy()
        X_with_missing[artificial_mask] = np.nan
        
        return X_with_missing, artificial_mask
    
    def _generate_masks(self, X, X_ori):
        """
        Generate all necessary masks for SAITS training.
        
        Args:
            X (np.ndarray): Input data with missing values
            X_ori (np.ndarray): Original data
            
        Returns:
            dict: Dictionary containing all masks
        """
        # Missing mask: 1 for observed values, 0 for missing
        missing_mask = ~np.isnan(X)
        
        # Original missing mask: 1 for originally observed values
        ori_missing_mask = ~np.isnan(X_ori)
        
        # Indicating mask: 1 for artificially missing values
        indicating_mask = ori_missing_mask & ~missing_mask
        
        return {
            'missing_mask': missing_mask.astype(np.float32),
            'indicating_mask': indicating_mask.astype(np.float32),
            'ori_missing_mask': ori_missing_mask.astype(np.float32)
        }
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        """
        Get a training sample.
        
        Returns:
            dict: Dictionary containing all necessary data for SAITS training
        """
        sequence = self.sequences[idx]
        X_ori = sequence['X'].copy()
        
        if self.return_X_ori:
            # Use original data for MIT loss calculation
            X_input = X_ori.copy()
        else:
            # Create artificial missing values
            X_input, _ = self._create_artificial_missing(X_ori)
        
        # Fill NaN values with zeros for model input
        X_filled = np.nan_to_num(X_input, nan=0.0)
        X_ori_filled = np.nan_to_num(X_ori, nan=0.0)
        
        # Generate masks
        masks = self._generate_masks(X_input, X_ori)
        
        # Convert to tensors
        sample = {
            'index': torch.tensor(sequence['index'], dtype=torch.long),
            'X': torch.tensor(X_filled, dtype=torch.float32),
            'X_ori': torch.tensor(X_ori_filled, dtype=torch.float32),
            'missing_mask': torch.tensor(masks['missing_mask'], dtype=torch.float32),
            'indicating_mask': torch.tensor(masks['indicating_mask'], dtype=torch.float32),
            'ori_missing_mask': torch.tensor(masks['ori_missing_mask'], dtype=torch.float32)
        }
        
        if self.return_y:
            sample['y'] = torch.tensor(sequence['y'], dtype=torch.float32)
        
        return sample
    
    def get_scaler(self):
        """Get the data scaler for inverse transformation."""
        return self.scaler_X, self.scaler_y


class EnhancedSAITSDataLoader:
    """
    Enhanced data loader for SAITS with advanced features.
    
    This class provides:
    - Automatic train/validation splitting
    - Proper batch collation for SAITS training
    - Data statistics and validation
    """
    
    def __init__(self, X, y=None, sequence_length=50, batch_size=32,
                 validation_split=0.2, missing_rate=0.1, 
                 missing_pattern='MCAR', standardize=True,
                 num_workers=0, shuffle=True):
        
        self.sequence_length = sequence_length
        self.batch_size = batch_size
        self.validation_split = validation_split
        self.missing_rate = missing_rate
        self.missing_pattern = missing_pattern
        self.standardize = standardize
        self.num_workers = num_workers
        self.shuffle = shuffle
        
        # Validate input data
        self._validate_data(X, y)
        
        # Split data
        self.X_train, self.X_val, self.y_train, self.y_val = self._split_data(X, y)
        
        # Create datasets
        self.train_dataset = EnhancedSAITSDataset(
            self.X_train, self.y_train, sequence_length, missing_rate,
            return_X_ori=True, missing_pattern=missing_pattern, 
            standardize=standardize
        )
        
        if self.X_val is not None:
            self.val_dataset = EnhancedSAITSDataset(
                self.X_val, self.y_val, sequence_length, missing_rate=0.0,  # No artificial missing in validation
                return_X_ori=True, missing_pattern=missing_pattern,
                standardize=False  # Use training scaler
            )
            
            # Apply training scaler to validation data
            if standardize:
                scaler_X, scaler_y = self.train_dataset.get_scaler()
                if scaler_X is not None:
                    self.val_dataset.X_scaled = scaler_X.transform(
                        self.X_val.reshape(-1, self.X_val.shape[-1])
                    ).reshape(self.X_val.shape)
                if scaler_y is not None and self.y_val is not None:
                    self.val_dataset.y_scaled = scaler_y.transform(
                        self.y_val.reshape(-1, 1)
                    ).flatten()
                
                # Regenerate sequences with scaled data
                self.val_dataset.sequences = self.val_dataset._generate_sequences()
        else:
            self.val_dataset = None
    
    def _validate_data(self, X, y):
        """Validate input data."""
        if not isinstance(X, np.ndarray):
            raise TypeError("X must be a numpy array")
        
        if len(X.shape) != 2:
            raise ValueError("X must be a 2D array (n_samples, n_features)")
        
        if y is not None:
            if not isinstance(y, np.ndarray):
                raise TypeError("y must be a numpy array")
            if len(y) != len(X):
                raise ValueError("X and y must have the same number of samples")
        
        if len(X) < self.sequence_length:
            raise ValueError(f"Data length ({len(X)}) must be >= sequence_length ({self.sequence_length})")
    
    def _split_data(self, X, y):
        """Split data into training and validation sets."""
        if self.validation_split <= 0:
            return X, None, y, None
        
        split_idx = int(len(X) * (1 - self.validation_split))
        
        X_train = X[:split_idx]
        X_val = X[split_idx:]
        
        if y is not None:
            y_train = y[:split_idx]
            y_val = y[split_idx:]
        else:
            y_train = None
            y_val = None
        
        return X_train, X_val, y_train, y_val
    
    def get_train_loader(self):
        """Get training data loader."""
        return DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=self.shuffle,
            num_workers=self.num_workers,
            pin_memory=torch.cuda.is_available()
        )
    
    def get_val_loader(self):
        """Get validation data loader."""
        if self.val_dataset is None:
            return None
        
        return DataLoader(
            self.val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=torch.cuda.is_available()
        )
    
    def get_data_info(self):
        """Get information about the data."""
        info = {
            'train_samples': len(self.train_dataset),
            'train_sequences': len(self.train_dataset.sequences),
            'sequence_length': self.sequence_length,
            'n_features': self.X_train.shape[1],
            'missing_rate': self.missing_rate,
            'missing_pattern': self.missing_pattern
        }
        
        if self.val_dataset is not None:
            info['val_samples'] = len(self.val_dataset)
            info['val_sequences'] = len(self.val_dataset.sequences)
        
        return info


def test_enhanced_data_handler():
    """Test the enhanced data handler."""
    print("🧪 Testing Enhanced SAITS Data Handler")
    
    # Create sample time series data
    n_samples, n_features = 200, 4
    X = np.random.randn(n_samples, n_features)
    y = np.random.randn(n_samples)
    
    # Add some missing values
    missing_mask = np.random.random(X.shape) < 0.1
    X[missing_mask] = np.nan
    
    print(f"📊 Data shape: {X.shape}")
    print(f"📊 Missing values: {np.isnan(X).sum()}")
    
    # Create data loader
    data_loader = EnhancedSAITSDataLoader(
        X, y, sequence_length=20, batch_size=8,
        validation_split=0.2, missing_rate=0.15
    )
    
    # Get data info
    info = data_loader.get_data_info()
    print(f"✅ Data Info: {info}")
    
    # Test training loader
    train_loader = data_loader.get_train_loader()
    val_loader = data_loader.get_val_loader()
    
    print(f"✅ Train batches: {len(train_loader)}")
    print(f"✅ Val batches: {len(val_loader) if val_loader else 0}")
    
    # Test a batch
    for batch in train_loader:
        print(f"✅ Batch keys: {list(batch.keys())}")
        print(f"✅ X shape: {batch['X'].shape}")
        print(f"✅ Missing mask shape: {batch['missing_mask'].shape}")
        print(f"✅ Indicating mask sum: {batch['indicating_mask'].sum()}")
        break
    
    print("\n🎉 Enhanced SAITS Data Handler test completed!")


if __name__ == "__main__":
    test_enhanced_data_handler()
