#!/usr/bin/env python3
"""
Debug script to isolate SAITS dimension issues.
"""

import torch
import numpy as np
from saits_model import SAITSRegressor

def test_saits_dimensions():
    """Test SAITS with minimal data to isolate dimension issues."""
    print("🔍 Testing SAITS dimensions...")
    
    # Create minimal test data
    batch_size = 4  # Small batch size
    sequence_length = 10  # Small sequence length
    n_features = 3  # Small number of features
    
    # Create synthetic data
    X = np.random.randn(50, n_features)  # 50 samples, 3 features
    y = np.random.randn(50)  # 50 targets
    
    # Add some missing values
    mask = np.random.random(X.shape) < 0.2
    X[mask] = np.nan
    
    print(f"Data shape: X={X.shape}, y={y.shape}")
    print(f"Missing values: {np.isnan(X).sum()}")
    
    # Create SAITS model with small dimensions
    model = SAITSRegressor(
        sequence_length=sequence_length,
        batch_size=batch_size,
        d_model=16,  # Small model dimension
        d_inner=32,
        n_head=2,
        d_k=8,
        d_v=8,
        epochs=1,  # Just one epoch for testing
        patience=1
    )
    
    try:
        print("🏋️  Fitting model...")
        model.fit(X, y, verbose=True)
        print("✅ Model fitted successfully!")
        
        print("🔮 Testing prediction...")
        predictions = model.predict(X[:10])  # Predict on first 10 samples
        print(f"✅ Predictions shape: {predictions.shape}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_tensor_operations():
    """Test specific tensor operations that might cause issues."""
    print("\n🔍 Testing tensor operations...")
    
    batch_size = 4
    sequence_length = 10
    d_model = 16
    n_features = 3
    
    # Test positional encoding
    print("Testing positional encoding...")
    from saits_model import PositionalEncoding
    
    pos_enc = PositionalEncoding(d_model, n_position=200)
    x = torch.randn(batch_size, sequence_length, d_model)
    
    print(f"Input shape: {x.shape}")
    print(f"PE buffer shape: {pos_enc.pe.shape}")
    
    try:
        output = pos_enc(x)
        print(f"✅ PE output shape: {output.shape}")
    except Exception as e:
        print(f"❌ PE error: {e}")
    
    # Test attention mechanism
    print("\nTesting attention mechanism...")
    from saits_model import MultiHeadAttention
    
    attn = MultiHeadAttention(d_model=d_model, n_head=2, d_k=8, d_v=8)
    
    try:
        q = k = v = torch.randn(batch_size, sequence_length, d_model)
        output, attn_weights = attn(q, k, v)
        print(f"✅ Attention output shape: {output.shape}")
        print(f"✅ Attention weights shape: {attn_weights.shape}")
    except Exception as e:
        print(f"❌ Attention error: {e}")

if __name__ == "__main__":
    test_tensor_operations()
    test_saits_dimensions()
