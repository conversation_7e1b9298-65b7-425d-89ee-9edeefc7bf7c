#!/usr/bin/env python3
"""
Test script for SAITS integration with the ML log prediction system.
"""

import numpy as np
import pandas as pd
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_saits_import():
    """Test if SAITS can be imported successfully."""
    print("Testing SAITS import...")
    try:
        from saits_model import SAITSRegressor
        print("✅ SAITS imported successfully")
        return True
    except ImportError as e:
        print(f"❌ SAITS import failed: {e}")
        return False

def test_model_registry():
    """Test if SAITS is properly registered in MODEL_REGISTRY."""
    print("\nTesting MODEL_REGISTRY...")
    try:
        from ml_core import MODEL_REGISTRY
        
        print(f"Available models: {list(MODEL_REGISTRY.keys())}")
        
        if 'saits' in MODEL_REGISTRY:
            print("✅ SAITS found in MODEL_REGISTRY")
            saits_config = MODEL_REGISTRY['saits']
            print(f"   Name: {saits_config['name']}")
            print(f"   Model class: {saits_config['model_class']}")
            print(f"   Hyperparameters: {list(saits_config['hyperparameters'].keys())}")
            return True
        else:
            print("❌ SAITS not found in MODEL_REGISTRY")
            return False
    except Exception as e:
        print(f"❌ MODEL_REGISTRY test failed: {e}")
        return False

def test_saits_basic_functionality():
    """Test basic SAITS functionality with synthetic data."""
    print("\nTesting SAITS basic functionality...")
    try:
        from saits_model import SAITSRegressor
        
        # Create synthetic well log data
        np.random.seed(42)
        n_samples = 200
        depth = np.linspace(1000, 1200, n_samples)
        
        # Simulate well log curves with some correlation
        gr = 50 + 30 * np.sin(depth / 50) + np.random.normal(0, 5, n_samples)
        nphi = 0.2 + 0.1 * np.cos(depth / 30) + np.random.normal(0, 0.02, n_samples)
        rhob = 2.3 + 0.2 * np.sin(depth / 40) + np.random.normal(0, 0.05, n_samples)
        dt = 80 + 20 * np.cos(depth / 60) + np.random.normal(0, 3, n_samples)
        
        # Create DataFrame
        data = pd.DataFrame({
            'MD': depth,
            'GR': gr,
            'NPHI': nphi,
            'RHOB': rhob,
            'DT': dt
        })
        
        # Introduce some missing values
        missing_indices = np.random.choice(n_samples, size=int(0.1 * n_samples), replace=False)
        data.loc[missing_indices, 'DT'] = np.nan
        
        # Prepare features and target
        feature_cols = ['GR', 'NPHI', 'RHOB', 'MD']
        target_col = 'DT'
        
        X = data[feature_cols]
        y = data[target_col]
        
        # Remove rows where target is missing for training
        train_mask = ~y.isna()
        X_train = X[train_mask]
        y_train = y[train_mask]
        
        print(f"   Training data shape: X={X_train.shape}, y={y_train.shape}")
        
        # Initialize SAITS model with small parameters for quick testing
        model = SAITSRegressor(
            sequence_length=20,
            epochs=5,  # Small number for testing
            batch_size=16,
            d_model=32,
            d_inner=64,
            n_head=2,
            patience=3,
            verbose=True
        )
        
        print("   Fitting SAITS model...")
        model.fit(X_train, y_train, verbose=True)
        
        print("   Making predictions...")
        predictions = model.predict(X)
        
        print(f"   Predictions shape: {predictions.shape}")
        print(f"   Non-NaN predictions: {np.sum(~np.isnan(predictions))}")
        
        # Calculate some basic metrics on available data
        valid_mask = ~np.isnan(predictions) & ~np.isnan(y)
        if np.sum(valid_mask) > 0:
            mae = np.mean(np.abs(predictions[valid_mask] - y[valid_mask]))
            print(f"   MAE on valid predictions: {mae:.3f}")
        
        print("✅ SAITS basic functionality test passed")
        return True
        
    except Exception as e:
        print(f"❌ SAITS basic functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_with_ml_core():
    """Test SAITS integration with the ml_core module."""
    print("\nTesting integration with ml_core...")
    try:
        from ml_core import MODEL_REGISTRY
        
        if 'saits' not in MODEL_REGISTRY:
            print("❌ SAITS not available in MODEL_REGISTRY")
            return False
        
        # Create synthetic data
        np.random.seed(42)
        n_samples = 100
        
        data = pd.DataFrame({
            'MD': np.linspace(1000, 1100, n_samples),
            'GR': 50 + np.random.normal(0, 10, n_samples),
            'NPHI': 0.2 + np.random.normal(0, 0.05, n_samples),
            'RHOB': 2.3 + np.random.normal(0, 0.1, n_samples),
            'DT': 80 + np.random.normal(0, 5, n_samples),
            'WELL': ['TEST_WELL'] * n_samples
        })
        
        # Test model instantiation from registry
        saits_config = MODEL_REGISTRY['saits']
        hyperparams = {param: meta['default'] for param, meta in saits_config['hyperparameters'].items()}
        hyperparams.update(saits_config['fixed_params'])
        
        # Reduce parameters for quick testing
        hyperparams['epochs'] = 3
        hyperparams['sequence_length'] = 15
        
        model = saits_config['model_class'](**hyperparams)
        
        print(f"   Created model: {model.__class__.__name__}")
        print("✅ Integration with ml_core test passed")
        return True
        
    except Exception as e:
        print(f"❌ Integration with ml_core test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🧪 Testing SAITS Integration")
    print("=" * 50)
    
    tests = [
        test_saits_import,
        test_model_registry,
        test_saits_basic_functionality,
        test_integration_with_ml_core
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"   Passed: {sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 All tests passed! SAITS integration is working.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return all(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
