#!/usr/bin/env python3
"""
Demonstration script showing SAITS integration with the ML log prediction system.

This script creates synthetic well log data and demonstrates how SAITS can be used
alongside traditional ML models for log prediction and imputation.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from ml_core import MODEL_REGISTRY, impute_logs, evaluate_model_comprehensive
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

def create_synthetic_well_data(n_wells=3, n_samples_per_well=300):
    """Create synthetic well log data with realistic patterns."""
    np.random.seed(42)
    
    all_data = []
    
    for well_idx in range(n_wells):
        well_name = f"WELL_{well_idx + 1:02d}"
        
        # Create depth array
        depth_start = 1000 + well_idx * 50  # Slightly different depth ranges
        depth_end = depth_start + 200
        depth = np.linspace(depth_start, depth_end, n_samples_per_well)
        
        # Create correlated log curves with realistic patterns
        # Gamma Ray (GR) - typically 0-200 API
        gr_base = 60 + 20 * np.sin(depth / 30) + 10 * np.cos(depth / 50)
        gr = gr_base + np.random.normal(0, 8, n_samples_per_well)
        gr = np.clip(gr, 10, 180)
        
        # Neutron Porosity (NPHI) - typically 0-0.6 v/v
        nphi_base = 0.25 + 0.1 * np.sin(depth / 40 + well_idx) + 0.05 * np.cos(depth / 25)
        nphi = nphi_base + np.random.normal(0, 0.03, n_samples_per_well)
        nphi = np.clip(nphi, 0.05, 0.5)
        
        # Bulk Density (RHOB) - typically 1.8-2.8 g/cc
        rhob_base = 2.4 - 0.3 * nphi + 0.1 * np.sin(depth / 35)
        rhob = rhob_base + np.random.normal(0, 0.05, n_samples_per_well)
        rhob = np.clip(rhob, 1.9, 2.7)
        
        # Sonic Transit Time (DT) - typically 40-140 us/ft
        # Correlated with porosity and density
        dt_base = 55 + 150 * nphi - 20 * (rhob - 2.0) + 10 * np.sin(depth / 45)
        dt = dt_base + np.random.normal(0, 4, n_samples_per_well)
        dt = np.clip(dt, 45, 120)
        
        # Create DataFrame for this well
        well_data = pd.DataFrame({
            'WELL': well_name,
            'MD': depth,
            'GR': gr,
            'NPHI': nphi,
            'RHOB': rhob,
            'DT': dt
        })
        
        all_data.append(well_data)
    
    # Combine all wells
    combined_data = pd.concat(all_data, ignore_index=True)
    
    # Introduce some missing values in target log (DT)
    n_total = len(combined_data)
    missing_indices = np.random.choice(n_total, size=int(0.15 * n_total), replace=False)
    combined_data.loc[missing_indices, 'DT'] = np.nan
    
    print(f"Created synthetic data:")
    print(f"  • {n_wells} wells")
    print(f"  • {n_total} total data points")
    print(f"  • {len(missing_indices)} missing DT values ({100*len(missing_indices)/n_total:.1f}%)")
    
    return combined_data

def compare_models(df, feature_cols, target_col):
    """Compare SAITS with traditional ML models."""
    print(f"\n🔬 Comparing Models for {target_col} Prediction")
    print("=" * 60)
    
    # Prepare data
    train_data = df[df[target_col].notna()].copy()
    X = train_data[feature_cols + ['MD']].apply(lambda c: c.fillna(c.mean()), axis=0)
    y = train_data[target_col]
    
    # Split data
    X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.25, random_state=42)
    
    print(f"Training data: {len(X_train)} samples")
    print(f"Validation data: {len(X_val)} samples")
    
    # Test different models
    models_to_test = {}
    
    # Traditional ML models (smaller parameters for demo)
    if 'xgboost' in MODEL_REGISTRY:
        from xgboost import XGBRegressor
        models_to_test['XGBoost'] = XGBRegressor(
            n_estimators=100, learning_rate=0.1, max_depth=4, random_state=42
        )
    
    if 'lightgbm' in MODEL_REGISTRY:
        from lightgbm import LGBMRegressor
        models_to_test['LightGBM'] = LGBMRegressor(
            n_estimators=100, learning_rate=0.1, random_state=42, verbose=-1
        )
    
    # SAITS model (smaller parameters for demo)
    if 'saits' in MODEL_REGISTRY:
        from saits_model import SAITSRegressor
        models_to_test['SAITS'] = SAITSRegressor(
            sequence_length=30,
            epochs=5,  # Reduce for debugging
            batch_size=16,
            d_model=32,
            d_inner=64,
            n_head=2,
            patience=5,
            random_state=42
        )
    
    # Train and evaluate models
    results = []
    
    for name, model in models_to_test.items():
        print(f"\n🤖 Training {name}...")
        try:
            # Train model
            if 'SAITS' in name:
                print(f"   Debug: X_train shape: {X_train.shape}, y_train shape: {y_train.shape}")
                print(f"   Debug: X_train NaN count: {X_train.isnull().sum().sum()}")
                print(f"   Debug: y_train NaN count: {y_train.isnull().sum()}")
                model.fit(X_train, y_train, verbose=True)
            else:
                model.fit(X_train, y_train)
            
            # Evaluate
            metrics = evaluate_model_comprehensive(model, X_train, y_train, X_val, y_val)
            metrics['model_name'] = name
            results.append(metrics)
            
            print(f"   ✅ {name}: MAE={metrics['mae']:.3f}, R²={metrics['r2']:.3f}")
            
        except Exception as e:
            print(f"   ❌ {name} failed: {e}")
    
    # Sort by composite score (lower is better)
    results.sort(key=lambda x: x['composite_score'])
    
    print(f"\n📊 Model Ranking (by composite score):")
    for i, result in enumerate(results, 1):
        print(f"   {i}. {result['model_name']}: "
              f"MAE={result['mae']:.3f}, "
              f"RMSE={result['rmse']:.3f}, "
              f"R²={result['r2']:.3f}")
    
    return results

def demonstrate_full_workflow(df):
    """Demonstrate the full ML workflow with SAITS included."""
    print(f"\n🔄 Full Workflow Demonstration")
    print("=" * 60)
    
    # Configuration
    feature_cols = ['GR', 'NPHI', 'RHOB']
    target_col = 'DT'
    wells = df['WELL'].unique().tolist()
    
    # Well separation configuration
    well_cfg = {
        'mode': 'mixed',  # Use all wells for training and prediction
        'training_wells': wells,
        'prediction_wells': wells
    }
    
    # Create models dictionary with all available models
    models = {}
    for model_key in MODEL_REGISTRY:
        model_config = MODEL_REGISTRY[model_key]
        
        # Get default hyperparameters
        hyperparams = {param: meta['default'] for param, meta in model_config['hyperparameters'].items()}
        hyperparams.update(model_config['fixed_params'])
        
        # Reduce parameters for demo speed
        if model_key == 'saits':
            hyperparams.update({
                'epochs': 15,
                'sequence_length': 25,
                'd_model': 32,
                'batch_size': 16
            })
        elif model_key in ['xgboost', 'lightgbm']:
            hyperparams['n_estimators'] = 100
        elif model_key == 'catboost':
            hyperparams['iterations'] = 200
        
        # Create model instance
        model_instance = model_config['model_class'](**hyperparams)
        models[model_config['name']] = model_instance
    
    print(f"Available models: {list(models.keys())}")
    
    # Run the full imputation workflow
    print(f"\n🚀 Running imputation workflow...")
    result_df, model_results = impute_logs(df, feature_cols, target_col, models, well_cfg, prediction_mode=1)
    
    if model_results:
        print(f"\n🏆 Best model: {model_results['best_model_name']}")
        
        # Show evaluation results
        print(f"\n📈 Model Performance:")
        for eval_result in model_results['evaluations']:
            print(f"   {eval_result['model_name']}: "
                  f"MAE={eval_result['mae']:.3f}, "
                  f"R²={eval_result['r2']:.3f}")
        
        # Calculate imputation statistics
        original_missing = df[target_col].isna().sum()
        imputed_col = f'{target_col}_imputed'
        
        if imputed_col in result_df.columns:
            still_missing = result_df[imputed_col].isna().sum()
            imputed_count = original_missing - still_missing
            
            print(f"\n📊 Imputation Results:")
            print(f"   Original missing values: {original_missing}")
            print(f"   Successfully imputed: {imputed_count}")
            print(f"   Still missing: {still_missing}")
            print(f"   Imputation rate: {100*imputed_count/original_missing:.1f}%")
    
    return result_df, model_results

def main():
    """Main demonstration function."""
    print("🎯 SAITS Integration Demonstration")
    print("=" * 80)
    
    # Check if SAITS is available
    if 'saits' not in MODEL_REGISTRY:
        print("❌ SAITS is not available in MODEL_REGISTRY")
        print("   Make sure PyTorch and other dependencies are installed")
        return
    
    print("✅ SAITS is available and ready to use!")
    
    # Create synthetic data
    print(f"\n📊 Creating synthetic well log data...")
    df = create_synthetic_well_data(n_wells=2, n_samples_per_well=200)
    
    # Show data overview
    print(f"\nData overview:")
    print(df.groupby('WELL').size())
    print(f"\nMissing values:")
    print(df.isnull().sum())
    
    # Compare models
    feature_cols = ['GR', 'NPHI', 'RHOB']
    target_col = 'DT'
    results = compare_models(df, feature_cols, target_col)
    
    # Demonstrate full workflow
    result_df, model_results = demonstrate_full_workflow(df)
    
    print(f"\n🎉 Demonstration completed successfully!")
    print(f"   SAITS has been successfully integrated into the ML log prediction system.")
    print(f"   You can now use SAITS alongside traditional ML models for well log imputation.")

if __name__ == "__main__":
    main()
